#pragma once
#include "../global.h"

// ... Existing content (if any) would be here, but since the file is new, start fresh ...
struct torque_config_s {
    bool torqueControlEnabled;
    float torqueRampRate;
    bool fallbackModeEnabled;
    
    // Tables
    float pedalToTorqueTable[PEDAL_TO_TORQUE_SIZE];
    float pedalPositionBins[PEDAL_TO_TORQUE_SIZE];
    
    float torquePerAirmassTable[TORQUE_CURVE_SIZE];
    float sparkEfficiencyTable[IGN_RPM_COUNT][IGN_LOAD_COUNT];
    float frictionTorqueTable[TORQUE_CURVE_SIZE];
    float maxTorqueTable[TORQUE_CURVE_SIZE];
    float torqueRpmBins[TORQUE_CURVE_SIZE];
    
    // Calibration parameters
    float manifoldVolume;
    float transientPredictionTime;
};

// Existing structs (if any) should remain, but since it's new, we'll only include the required struct
