/**
 * @file TorqueControl.cpp
 * @brief Enhanced Torque-Based Control System Implementation
 */

#include "pch.h"
#include "TorqueControl.h"

// ==================== ENHANCED SENSOR HANDLING ====================
float TorqueSensorManager::getValidatedSensor(SensorType type, float fallback, bool useCache) {
    auto result = Sensor::get(type);
    efitick_t now = getTimeNowNt();
    int idx = static_cast<int>(type) % 8;  // Simple hash for indexing
    
    if (result.Valid) {
        lastValidReadings[idx] = now;
        lastValidValues[idx] = result.Value;
        return result.Value;
    }
    
    // Check if we have recent cached value
    if (useCache && (now - lastValidReadings[idx]) < MS2NT(engineConfiguration->torqueConfig.sensorTimeoutMs)) {
        return lastValidValues[idx];
    }
    
    // Log fault and return fallback
    firmwareError(ObdCode::OBD_Torque_Sensor_Fault, "Sensor %d failed", static_cast<int>(type));
    return fallback;
}

bool TorqueSensorManager::isSensorHealthy(SensorType type) {
    efitick_t now = getTimeNowNt();
    int idx = static_cast<int>(type) % 8;
    return (now - lastValidReadings[idx]) < MS2NT(engineConfiguration->torqueConfig.sensorTimeoutMs);
}

// ==================== ENHANCED TORQUE MODEL ====================
float RusEFITorqueModel::estimateEngineTorque(float airmass, float sparkAdvance, float lambda, float rpm) {
    ScopePerf perf(PE::TorqueEstimation);
    
    if (rpm < 100) return 0;  // Engine not running
    
    // Base torque from airmass and VE
    float baseTorque = interpolate2d(
        rpm,
        engineConfiguration->torqueConfig.torqueRpmBins,
        engineConfiguration->torqueConfig.torquePerAirmassTable,
        TORQUE_CURVE_SIZE
    ) * airmass;
    
    // Spark efficiency correction
    float sparkEff = getSparkEfficiency(rpm, airmass, sparkAdvance);
    
    // Lambda correction (simplified rich/lean efficiency)
    float lambdaEff = getLambdaEfficiency(lambda);
    
    // Friction losses
    float frictionTorque = interpolate2d(
        rpm,
        engineConfiguration->torqueConfig.torqueRpmBins,
        engineConfiguration->torqueConfig.frictionTorqueTable,
        TORQUE_CURVE_SIZE
    );
    
    float estimatedTorque = (baseTorque * sparkEff * lambdaEff) - frictionTorque;
    
    // Low-pass filter for stability
    float alpha = 0.1f;  // Smoothing factor
    lastEstimatedTorque = alpha * estimatedTorque + (1.0f - alpha) * lastEstimatedTorque;
    lastUpdateTime = getTimeNowNt();
    
    return clampF(-50.0f, lastEstimatedTorque, 500.0f);
}

float RusEFITorqueModel::getSparkEfficiency(float rpm, float load, float sparkAdvance) {
    // Simple spark efficiency model
    float optimalSpark = 25.0f;  // MBT timing
    float efficiency = 1.0f - abs(sparkAdvance - optimalSpark) * 0.02f;
    return clampF(0.3f, efficiency, 1.0f);
}

float RusEFITorqueModel::getLambdaEfficiency(float lambda) {
    // Peak efficiency around stoichiometric
    float targetLambda = 1.0f;
    float deviation = abs(lambda - targetLambda);
    return clampF(0.7f, 1.0f - deviation * 0.1f, 1.0f);
}

// ==================== ENHANCED ACTUATOR CONTROL ====================
void TorqueActuatorController::init() {
    torquePid.initPidClass(&engineConfiguration->torqueConfig.torquePid);
}

void TorqueActuatorController::setTargetTorque(float targetTorque, float currentTorque, float rpm) {
    // PID control for torque tracking
    float torqueError = targetTorque - currentTorque;
    float airmassCorrection = torquePid.getOutput(torqueError, rpm);
    
    // Convert torque to target airmass
    float baseAirmass = torqueToAirmass(targetTorque, rpm);
    float targetAirmass = baseAirmass + airmassCorrection;
    
    // Rate limiting
    float maxDelta = engineConfiguration->torqueConfig.torqueRampRate * 0.01f; // 10ms cycle
    targetAirmass = clampF(
        lastTargetAirmass - maxDelta,
        targetAirmass,
        lastTargetAirmass + maxDelta
    );
    
    lastTargetAirmass = targetAirmass;
    setTargetAirmass(targetAirmass);
}

float TorqueActuatorController::torqueToAirmass(float torque, float rpm) {
    // Inverse of torque model
    float frictionTorque = interpolate2d(
        rpm,
        engineConfiguration->torqueConfig.torqueRpmBins,
        engineConfiguration->torqueConfig.frictionTorqueTable,
        TORQUE_CURVE_SIZE
    );
    
    float netTorque = torque + frictionTorque;
    
    float torquePerAirmass = interpolate2d(
        rpm,
        engineConfiguration->torqueConfig.torqueRpmBins,
        engineConfiguration->torqueConfig.torquePerAirmassTable,
        TORQUE_CURVE_SIZE
    );
    
    return (torquePerAirmass > 0.1f) ? netTorque / torquePerAirmass : 0;
}

void TorqueActuatorController::setTargetAirmass(float target) {
    // For now, just store the target - actual ETB integration would go here
    engine->outputChannels.targetAirmass = target;
}

// ==================== ENHANCED CAN COMMUNICATION ====================
void TorqueCANManager::processTCURequest(const CANRxFrame& frame) {
    if (frame.DLC != 8) {  // Enhanced 8-byte message
        return;
    }
    
    // Parse enhanced TCU message
    int16_t torque_raw = (frame.data8[1] << 8) | frame.data8[0];
    uint8_t priority = frame.data8[2];
    uint8_t flags = frame.data8[3];
    uint16_t sequence = (frame.data8[5] << 8) | frame.data8[4];
    uint16_t checksum = (frame.data8[7] << 8) | frame.data8[6];
    
    // Validate checksum (simple XOR)
    uint16_t calcChecksum = torque_raw ^ priority ^ flags ^ sequence;
    if (calcChecksum != checksum) {
        warning(ObdCode::OBD_Torque_CAN_Checksum, "TCU checksum error");
        return;
    }
    
    float torque_nm = torque_raw * 0.125f;
    
    // Enhanced validation
    if (torque_nm < engineConfiguration->torqueConfig.minTorqueLimit || 
        torque_nm > engineConfiguration->torqueConfig.maxTorqueLimit) {
        warning(ObdCode::OBD_Torque_CAN_Invalid, "TCU torque out of range: %.1f", torque_nm);
        return;
    }
    
    // Store with enhanced metadata
    torqueControlModule.tcuTorqueRequest = torque_nm;
    torqueControlModule.tcuPriority = priority;
    torqueControlModule.tcuFlags = flags;
    torqueControlModule.tcuRequestActive = true;
    lastTCUMessage = getTimeNowNt();
}

void TorqueCANManager::sendTorqueStatus() {
    // CAN transmission would be implemented here
    // For now, just update the message counter
    canMessageCounter++;
}

bool TorqueCANManager::isTCURequestValid() {
    efitick_t now = getTimeNowNt();
    return (now - lastTCUMessage) < MS2NT(engineConfiguration->torqueConfig.tcuTimeoutMs);
}

uint8_t TorqueCANManager::buildStatusFlags() {
    uint8_t flags = 0;
    if (engineConfiguration->torqueConfig.torqueControlEnabled) flags |= 0x01;
    if (torqueControlModule.fallbackModeActive) flags |= 0x02;
    if (torqueControlModule.limiterActive) flags |= 0x04;
    if (isTCURequestValid()) flags |= 0x08;
    return flags;
}

// ==================== ENHANCED MAIN TORQUE CONTROL MODULE ====================
void TorqueControl::onSlowCallback() {
    if (!engineConfiguration->torqueConfig.torqueControlEnabled) {
        return;
    }

    // Slow tasks (100Hz)
    canManager.sendTorqueStatus();
    updateDiagnostics();
}

void TorqueControl::onFastCallback() {
    if (!engineConfiguration->torqueConfig.torqueControlEnabled) {
        return;
    }

    ScopePerf perf(PE::TorqueControl);

    // Get validated sensor data
    float pedal = sensorManager.getValidatedSensor(SensorType::AcceleratorPedal, 0);
    float rpm = sensorManager.getValidatedSensor(SensorType::Rpm, 0);

    // Only run above cranking RPM
    if (rpm < engineConfiguration->cranking.rpm) {
        return;
    }

    // Calculate torque requests
    float pedalTorque = calculatePedalTorque(pedal);
    float targetTorque = arbitrateRequests(pedalTorque, rpm);

    // Get current torque estimate
    float currentTorque = getActualTorque();

    // Control actuators
    actuatorController.setTargetTorque(targetTorque, currentTorque, rpm);

    // Update output channels
    updateOutputChannels(targetTorque, currentTorque);

    lastControlUpdate = getTimeNowNt();
}

float TorqueControl::getActualTorque() {
    float airmass = sensorManager.getValidatedSensor(SensorType::AirMass, 0);
    float spark = engine->engineState.timingAdvance[0];
    float lambda = sensorManager.getValidatedSensor(SensorType::Lambda1, 14.7f);
    float rpm = sensorManager.getValidatedSensor(SensorType::Rpm, 0);

    return torqueModel.estimateEngineTorque(airmass, spark, lambda, rpm);
}

void TorqueControl::init() {
    actuatorController.init();
    initializeTables();
}

float TorqueControl::calculatePedalTorque(float pedalPosition) {
    return interpolate2d(
        pedalPosition,
        engineConfiguration->torqueConfig.pedalPositionBins,
        engineConfiguration->torqueConfig.pedalToTorqueTable,
        PEDAL_TO_TORQUE_SIZE
    );
}

float TorqueControl::arbitrateRequests(float pedalTorque, float rpm) {
    float targetTorque = pedalTorque;
    uint8_t limiterFlags = 0;

    // TCU request (highest priority)
    if (canManager.isTCURequestValid() && tcuRequestActive) {
        targetTorque = tcuTorqueRequest;
    }

    // Apply limits
    float maxAllowed = interpolate2d(
        rpm,
        engineConfiguration->torqueConfig.torqueRpmBins,
        engineConfiguration->torqueConfig.maxTorqueTable,
        TORQUE_CURVE_SIZE
    );

    if (targetTorque > maxAllowed) {
        targetTorque = maxAllowed;
        limiterFlags |= 0x01;  // Max torque limit active
    }

    // Rate limiting
    efitick_t now = getTimeNowNt();
    float dt = NT2SF(now - lastControlUpdate);
    float maxDelta = engineConfiguration->torqueConfig.torqueRampRate * dt;

    if (targetTorque > rampedTorqueTarget + maxDelta) {
        targetTorque = rampedTorqueTarget + maxDelta;
        limiterFlags |= 0x02;  // Rate limiter active
    } else if (targetTorque < rampedTorqueTarget - maxDelta) {
        targetTorque = rampedTorqueTarget - maxDelta;
        limiterFlags |= 0x02;  // Rate limiter active
    }

    rampedTorqueTarget = targetTorque;
    this->limiterFlags = limiterFlags;

    return targetTorque;
}

void TorqueControl::updateOutputChannels(float target, float actual) {
    engine->outputChannels.targetTorque = target;
    engine->outputChannels.actualTorque = actual;
    engine->outputChannels.torqueError = target - actual;
    engine->outputChannels.torqueLimiterFlags = limiterFlags;
}

void TorqueControl::updateDiagnostics() {
    // Check sensor health
    engine->outputChannels.pedalSensorOk = sensorManager.isSensorHealthy(SensorType::AcceleratorPedal);
    engine->outputChannels.airmassSensorOk = sensorManager.isSensorHealthy(SensorType::AirMass);

    // Check system health
    engine->outputChannels.torqueControlActive = engineConfiguration->torqueConfig.torqueControlEnabled;
    engine->outputChannels.tcuCommOk = canManager.isTCURequestValid();
}

void TorqueControl::initializeTables() {
    // Initialize default tables if not configured
    bool tablesEmpty = true;
    for (int i = 0; i < TORQUE_CURVE_SIZE && tablesEmpty; i++) {
        if (engineConfiguration->torqueConfig.torqueRpmBins[i] > 0) {
            tablesEmpty = false;
        }
    }

    if (tablesEmpty) {
        initTorqueTables();
    }
}

// ==================== TABLE INITIALIZATION ====================
void initTorqueTables() {
    // RPM bins (500-7000 RPM)
    for (int i = 0; i < TORQUE_CURVE_SIZE; i++) {
        float rpm = 500 + i * (6500.0f / (TORQUE_CURVE_SIZE - 1));
        engineConfiguration->torqueConfig.torqueRpmBins[i] = rpm;

        // Typical naturally aspirated gasoline engine characteristics
        engineConfiguration->torqueConfig.torquePerAirmassTable[i] = 12.0f + sin(rpm / 1000.0f) * 2.0f;
        engineConfiguration->torqueConfig.frictionTorqueTable[i] = 15.0f + rpm * 0.002f;

        // Peak torque around 4000 RPM
        float torqueFactor = 1.0f - pow((rpm - 4000.0f) / 3000.0f, 2) * 0.3f;
        engineConfiguration->torqueConfig.maxTorqueTable[i] = 250.0f * torqueFactor;
    }

    // Pedal position bins (0-100%)
    for (int i = 0; i < PEDAL_TO_TORQUE_SIZE; i++) {
        float pos = i * (100.0f / (PEDAL_TO_TORQUE_SIZE - 1));
        engineConfiguration->torqueConfig.pedalPositionBins[i] = pos;

        // Progressive pedal mapping
        engineConfiguration->torqueConfig.pedalToTorqueTable[i] = pow(pos / 100.0f, 1.2f) * 200.0f;
    }

    // Default limits
    engineConfiguration->torqueConfig.maxTorqueLimit = 300.0f;
    engineConfiguration->torqueConfig.minTorqueLimit = -50.0f;
    engineConfiguration->torqueConfig.torqueRampRate = 500.0f;  // Nm/s
    engineConfiguration->torqueConfig.tcuTimeoutMs = 200;
    engineConfiguration->torqueConfig.sensorTimeoutMs = 100;

    // Initialize PID parameters
    engineConfiguration->torqueConfig.torquePid.pFactor = 0.5f;
    engineConfiguration->torqueConfig.torquePid.iFactor = 0.1f;
    engineConfiguration->torqueConfig.torquePid.dFactor = 0.05f;
    engineConfiguration->torqueConfig.torquePid.offset = 0;
    engineConfiguration->torqueConfig.torquePid.minValue = -50;
    engineConfiguration->torqueConfig.torquePid.maxValue = 50;
}

// Global instance
TorqueControl torqueControlModule;
