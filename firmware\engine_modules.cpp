#include "pch.h"
#include "TorqueControl.h"

// List of all engine modules
std::vector<EngineModule*> engineModules = {
    &torqueControlModule,
    // Add other modules here
};

void initEngineModules() {
    for (auto module : engineModules) {
        module->init();
    }
}

// Initialize torque control with default values
void initTorqueControlDefaults() {
    // Enable torque control by default
    engineConfiguration->torqueConfig.torqueControlEnabled = true;
    engineConfiguration->torqueConfig.fallbackModeEnabled = true;

    // Set reasonable defaults
    engineConfiguration->torqueConfig.torqueRampRate = 500.0f;  // Nm/s
    engineConfiguration->torqueConfig.maxTorqueLimit = 300.0f;  // Nm
    engineConfiguration->torqueConfig.minTorqueLimit = -50.0f;  // Nm
    engineConfiguration->torqueConfig.tcuTimeoutMs = 200;       // ms
    engineConfiguration->torqueConfig.sensorTimeoutMs = 100;    // ms

    // Initialize tables if they're empty
    initTorqueTables();
}
